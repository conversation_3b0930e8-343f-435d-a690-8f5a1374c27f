'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

function isRelativeHref(href) {
  const pathname = typeof href === 'object' ? href.pathname : href;
  return pathname != null && !pathname.startsWith('/');
}
function isLocalHref(href) {
  if (typeof href === 'object') {
    return href.host == null && href.hostname == null;
  } else {
    const hasProtocol = /^[a-z]+:/i.test(href);
    return !hasProtocol;
  }
}
function isLocalizableHref(href) {
  return isLocalHref(href) && !isRelativeHref(href);
}
function localizeHref(href, locale) {
  let curLocale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : locale;
  let curPathname = arguments.length > 3 ? arguments[3] : undefined;
  let prefix = arguments.length > 4 ? arguments[4] : undefined;
  if (!isLocalizableHref(href)) {
    return href;
  }
  const isSwitchingLocale = locale !== curLocale;
  const isPathnamePrefixed = hasPathnamePrefixed(prefix, curPathname);
  const shouldPrefix = isSwitchingLocale || isPathnamePrefixed;
  if (shouldPrefix && prefix != null) {
    return prefixHref(href, prefix);
  }
  return href;
}
function prefixHref(href, prefix) {
  let prefixedHref;
  if (typeof href === 'string') {
    prefixedHref = prefixPathname(prefix, href);
  } else {
    prefixedHref = {
      ...href
    };
    if (href.pathname) {
      prefixedHref.pathname = prefixPathname(prefix, href.pathname);
    }
  }
  return prefixedHref;
}
function unprefixPathname(pathname, prefix) {
  return pathname.replace(new RegExp("^".concat(prefix)), '') || '/';
}
function prefixPathname(prefix, pathname) {
  let localizedHref = prefix;

  // Avoid trailing slashes
  if (/^\/(\?.*)?$/.test(pathname)) {
    pathname = pathname.slice(1);
  }
  localizedHref += pathname;
  return localizedHref;
}
function hasPathnamePrefixed(prefix, pathname) {
  return pathname === prefix || pathname.startsWith("".concat(prefix, "/"));
}
function hasTrailingSlash() {
  try {
    // Provided via `env` setting in `next.config.js` via the plugin
    return process.env._next_intl_trailing_slash === 'true';
  } catch (_unused) {
    return false;
  }
}
function normalizeTrailingSlash(pathname) {
  const trailingSlash = hasTrailingSlash();
  if (pathname !== '/') {
    const pathnameEndsWithSlash = pathname.endsWith('/');
    if (trailingSlash && !pathnameEndsWithSlash) {
      pathname += '/';
    } else if (!trailingSlash && pathnameEndsWithSlash) {
      pathname = pathname.slice(0, -1);
    }
  }
  return pathname;
}
function matchesPathname(/** E.g. `/users/[userId]-[userName]` */
template, /** E.g. `/users/23-jane` */
pathname) {
  const normalizedTemplate = normalizeTrailingSlash(template);
  const normalizedPathname = normalizeTrailingSlash(pathname);
  const regex = templateToRegex(normalizedTemplate);
  return regex.test(normalizedPathname);
}
function getLocalePrefix(locale, localePrefix) {
  var _localePrefix$prefixe;
  return localePrefix.mode !== 'never' && ((_localePrefix$prefixe = localePrefix.prefixes) === null || _localePrefix$prefixe === void 0 ? void 0 : _localePrefix$prefixe[locale]) ||
  // We return a prefix even if `mode: 'never'`. It's up to the consumer
  // to decide to use it or not.
  getLocaleAsPrefix(locale);
}
function getLocaleAsPrefix(locale) {
  return '/' + locale;
}
function templateToRegex(template) {
  const regexPattern = template
  // Replace optional catchall ('[[...slug]]')
  .replace(/\[\[(\.\.\.[^\]]+)\]\]/g, '?(.*)')
  // Replace catchall ('[...slug]')
  .replace(/\[(\.\.\.[^\]]+)\]/g, '(.+)')
  // Replace regular parameter ('[slug]')
  .replace(/\[([^\]]+)\]/g, '([^/]+)');
  return new RegExp("^".concat(regexPattern, "$"));
}
function isOptionalCatchAllSegment(pathname) {
  return pathname.includes('[[...');
}
function isCatchAllSegment(pathname) {
  return pathname.includes('[...');
}
function isDynamicSegment(pathname) {
  return pathname.includes('[');
}
function comparePathnamePairs(a, b) {
  const pathA = a.split('/');
  const pathB = b.split('/');
  const maxLength = Math.max(pathA.length, pathB.length);
  for (let i = 0; i < maxLength; i++) {
    const segmentA = pathA[i];
    const segmentB = pathB[i];

    // If one of the paths ends, prioritize the shorter path
    if (!segmentA && segmentB) return -1;
    if (segmentA && !segmentB) return 1;
    if (!segmentA && !segmentB) continue;

    // Prioritize static segments over dynamic segments
    if (!isDynamicSegment(segmentA) && isDynamicSegment(segmentB)) return -1;
    if (isDynamicSegment(segmentA) && !isDynamicSegment(segmentB)) return 1;

    // Prioritize non-catch-all segments over catch-all segments
    if (!isCatchAllSegment(segmentA) && isCatchAllSegment(segmentB)) return -1;
    if (isCatchAllSegment(segmentA) && !isCatchAllSegment(segmentB)) return 1;

    // Prioritize non-optional catch-all segments over optional catch-all segments
    if (!isOptionalCatchAllSegment(segmentA) && isOptionalCatchAllSegment(segmentB)) {
      return -1;
    }
    if (isOptionalCatchAllSegment(segmentA) && !isOptionalCatchAllSegment(segmentB)) {
      return 1;
    }
    if (segmentA === segmentB) continue;
  }

  // Both pathnames are completely static
  return 0;
}
function getSortedPathnames(pathnames) {
  return pathnames.sort(comparePathnamePairs);
}

exports.getLocaleAsPrefix = getLocaleAsPrefix;
exports.getLocalePrefix = getLocalePrefix;
exports.getSortedPathnames = getSortedPathnames;
exports.hasPathnamePrefixed = hasPathnamePrefixed;
exports.isLocalizableHref = isLocalizableHref;
exports.localizeHref = localizeHref;
exports.matchesPathname = matchesPathname;
exports.normalizeTrailingSlash = normalizeTrailingSlash;
exports.prefixHref = prefixHref;
exports.prefixPathname = prefixPathname;
exports.templateToRegex = templateToRegex;
exports.unprefixPathname = unprefixPathname;
