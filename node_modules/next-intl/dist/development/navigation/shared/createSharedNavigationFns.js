'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _rollupPluginBabelHelpers = require('../../_virtual/_rollupPluginBabelHelpers.js');
var navigation = require('next/navigation');
var React = require('react');
var config = require('../../routing/config.js');
var utils$1 = require('../../shared/utils.js');
var BaseLink = require('./BaseLink.js');
var utils = require('./utils.js');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var React__default = /*#__PURE__*/_interopDefault(React);

/**
 * Shared implementations for `react-server` and `react-client`
 */
function createSharedNavigationFns(getLocale, routing) {
  const config$1 = config.receiveRoutingConfig(routing || {});
  {
    utils.validateReceivedConfig(config$1);
  }
  const pathnames = config$1.pathnames;

  // This combination requires that the current host is known in order to
  // compute a correct pathname. Since that can only be achieved by reading from
  // headers, this would break static rendering. Therefore, as a workaround we
  // always add a prefix in this case to be on the safe side. The downside is
  // that the user might get redirected again if the middleware detects that the
  // prefix is not needed.
  const forcePrefixSsr = config$1.localePrefix.mode === 'as-needed' && config$1.domains || undefined;
  function Link(_ref, ref) {
    let {
      href,
      locale,
      ...rest
    } = _ref;
    let pathname, params;
    if (typeof href === 'object') {
      pathname = href.pathname;
      // @ts-expect-error -- This is ok
      params = href.params;
    } else {
      pathname = href;
    }

    // @ts-expect-error -- This is ok
    const isLocalizable = utils$1.isLocalizableHref(href);
    const localePromiseOrValue = getLocale();
    const curLocale = localePromiseOrValue instanceof Promise ? React.use(localePromiseOrValue) : localePromiseOrValue;
    const finalPathname = isLocalizable ? getPathname(
    // @ts-expect-error -- This is ok
    {
      locale: locale || curLocale,
      href: pathnames == null ? pathname : {
        pathname,
        params
      }
    }, locale != null || forcePrefixSsr || undefined) : pathname;
    return /*#__PURE__*/React__default.default.createElement(BaseLink.default, _rollupPluginBabelHelpers.extends({
      ref: ref
      // @ts-expect-error -- Available after the validation
      ,
      defaultLocale: config$1.defaultLocale
      // @ts-expect-error -- This is ok
      ,
      href: typeof href === 'object' ? {
        ...href,
        pathname: finalPathname
      } : finalPathname,
      locale: locale,
      localeCookie: config$1.localeCookie
      // Provide the minimal relevant information to the client side in order
      // to potentially remove the prefix in case of the `forcePrefixSsr` case
      ,
      unprefixed: forcePrefixSsr && isLocalizable ? {
        domains: config$1.domains.reduce((acc, domain) => {
          // @ts-expect-error -- This is ok
          acc[domain.domain] = domain.defaultLocale;
          return acc;
        }, {}),
        pathname: getPathname(
        // @ts-expect-error -- This is ok
        {
          locale: curLocale,
          href: pathnames == null ? pathname : {
            pathname,
            params
          }
        }, false)
      } : undefined
    }, rest));
  }
  const LinkWithRef = /*#__PURE__*/React.forwardRef(Link);
  function getPathname(args, /** @private Removed in types returned below */
  _forcePrefix) {
    const {
      href,
      locale
    } = args;
    let pathname;
    if (pathnames == null) {
      if (typeof href === 'object') {
        pathname = href.pathname;
        if (href.query) {
          pathname += utils.serializeSearchParams(href.query);
        }
      } else {
        pathname = href;
      }
    } else {
      pathname = utils.compileLocalizedPathname({
        locale,
        // @ts-expect-error -- This is ok
        ...utils.normalizeNameOrNameWithParams(href),
        // @ts-expect-error -- This is ok
        pathnames: config$1.pathnames
      });
    }
    return utils.applyPathnamePrefix(pathname, locale, config$1,
    // @ts-expect-error -- This is ok
    args.domain, _forcePrefix);
  }
  function getRedirectFn(fn) {
    /** @see https://next-intl.dev/docs/routing/navigation#redirect */
    return function redirectFn(args) {
      for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
        rest[_key - 1] = arguments[_key];
      }
      return fn(
      // @ts-expect-error -- We're forcing the prefix when no domain is provided
      getPathname(args, args.domain ? undefined : forcePrefixSsr), ...rest);
    };
  }
  const redirect = getRedirectFn(navigation.redirect);
  const permanentRedirect = getRedirectFn(navigation.permanentRedirect);
  return {
    config: config$1,
    Link: LinkWithRef,
    redirect,
    permanentRedirect,
    // Remove `_forcePrefix` from public API
    getPathname: getPathname
  };
}

exports.default = createSharedNavigationFns;
