'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var navigation = require('next/navigation');
var React = require('react');
var core = require('use-intl/core');
var RequestLocale = require('./RequestLocale.js');
var RequestLocaleLegacy = require('./RequestLocaleLegacy.js');
var getRuntimeConfig = require('next-intl/config');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var getRuntimeConfig__default = /*#__PURE__*/_interopDefault(getRuntimeConfig);

let hasWarnedForMissingReturnedLocale = false;
let hasWarnedForAccessedLocaleParam = false;

// Make sure `now` is consistent across the request in case none was configured
function getDefaultNowImpl() {
  return new Date();
}
const getDefaultNow = React.cache(getDefaultNowImpl);

// This is automatically inherited by `NextIntlClientProvider` if
// the component is rendered from a Server Component
function getDefaultTimeZoneImpl() {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
}
const getDefaultTimeZone = React.cache(getDefaultTimeZoneImpl);
async function receiveRuntimeConfigImpl(getConfig, localeOverride) {
  if (typeof getConfig !== 'function') {
    throw new Error("Invalid i18n request configuration detected.\n\nPlease verify that:\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\n2. You have a default export in your i18n request configuration file.\n\nSee also: https://next-intl.dev/docs/usage/configuration#i18n-request\n");
  }
  const params = {
    // In case the consumer doesn't read `params.locale` and instead provides the
    // `locale` (either in a single-language workflow or because the locale is
    // read from the user settings), don't attempt to read the request locale.
    get locale() {
      if (!hasWarnedForAccessedLocaleParam) {
        console.warn("\nThe `locale` parameter in `getRequestConfig` is deprecated, please switch to `await requestLocale`. See https://next-intl.dev/blog/next-intl-3-22#await-request-locale\n");
        hasWarnedForAccessedLocaleParam = true;
      }
      return localeOverride || RequestLocaleLegacy.getRequestLocale();
    },
    get requestLocale() {
      return localeOverride ? Promise.resolve(localeOverride) : RequestLocale.getRequestLocale();
    }
  };
  let result = getConfig(params);
  if (result instanceof Promise) {
    result = await result;
  }
  let locale = result.locale;
  if (!locale) {
    if (!hasWarnedForMissingReturnedLocale) {
      console.error("\nA `locale` is expected to be returned from `getRequestConfig`, but none was returned. This will be an error in the next major version of next-intl.\n\nSee: https://next-intl.dev/blog/next-intl-3-22#await-request-locale\n");
      hasWarnedForMissingReturnedLocale = true;
    }
    locale = await params.requestLocale;
    if (!locale) {
      {
        console.error("\nUnable to find `next-intl` locale because the middleware didn't run on this request and no `locale` was returned in `getRequestConfig`. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\n");
      }
      navigation.notFound();
    }
  }
  return {
    ...result,
    locale,
    now: result.now || getDefaultNow(),
    timeZone: result.timeZone || getDefaultTimeZone()
  };
}
const receiveRuntimeConfig = React.cache(receiveRuntimeConfigImpl);
const getFormatters = React.cache(core._createIntlFormatters);
const getCache = React.cache(core._createCache);
async function getConfigImpl(localeOverride) {
  const runtimeConfig = await receiveRuntimeConfig(getRuntimeConfig__default.default, localeOverride);
  return {
    ...core.initializeConfig(runtimeConfig),
    _formatters: getFormatters(getCache())
  };
}
const getConfig = React.cache(getConfigImpl);

exports.default = getConfig;
