"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("next/navigation"),t=require("react"),n=require("use-intl/core"),r=require("./RequestLocale.js"),o=require("./RequestLocaleLegacy.js");function c(e){return e&&e.__esModule?e:{default:e}}var a=c(require("next-intl/config"));const i=t.cache((function(){return new Date}));const u=t.cache((function(){return Intl.DateTimeFormat().resolvedOptions().timeZone}));const s=t.cache((async function(t,n){const c={get locale(){return n||o.getRequestLocale()},get requestLocale(){return n?Promise.resolve(n):r.getRequestLocale()}};let a=t(c);a instanceof Promise&&(a=await a);let s=a.locale;return s||(s=await c.requestLocale,s||e.notFound()),{...a,locale:s,now:a.now||i(),timeZone:a.timeZone||u()}})),l=t.cache(n._createIntlFormatters),f=t.cache(n._createCache);const q=t.cache((async function(e){const t=await s(a.default,e);return{...n.initializeConfig(t),_formatters:l(f())}}));exports.default=q;
