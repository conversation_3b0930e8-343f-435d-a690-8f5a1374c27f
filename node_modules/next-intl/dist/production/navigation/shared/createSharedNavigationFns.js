"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../../_virtual/_rollupPluginBabelHelpers.js"),a=require("next/navigation"),r=require("react"),t=require("../../routing/config.js"),n=require("../../shared/utils.js"),o=require("./BaseLink.js"),i=require("./utils.js");function l(e){return e&&e.__esModule?e:{default:e}}var u=l(r);exports.default=function(l,c){const s=t.receiveRoutingConfig(c||{}),f=s.pathnames,d="as-needed"===s.localePrefix.mode&&s.domains||void 0;function m(a,t){let i,c,{href:m,locale:p,...v}=a;"object"==typeof m?(i=m.pathname,c=m.params):i=m;const P=n.isLocalizableHref(m),g=l(),j=g instanceof Promise?r.use(g):g,q=P?h({locale:p||j,href:null==f?i:{pathname:i,params:c}},null!=p||d||void 0):i;return u.default.createElement(o.default,e.extends({ref:t,defaultLocale:s.defaultLocale,href:"object"==typeof m?{...m,pathname:q}:q,locale:p,localeCookie:s.localeCookie,unprefixed:d&&P?{domains:s.domains.reduce(((e,a)=>(e[a.domain]=a.defaultLocale,e)),{}),pathname:h({locale:j,href:null==f?i:{pathname:i,params:c}},!1)}:void 0},v))}const p=r.forwardRef(m);function h(e,a){const{href:r,locale:t}=e;let n;return null==f?"object"==typeof r?(n=r.pathname,r.query&&(n+=i.serializeSearchParams(r.query))):n=r:n=i.compileLocalizedPathname({locale:t,...i.normalizeNameOrNameWithParams(r),pathnames:s.pathnames}),i.applyPathnamePrefix(n,t,s,e.domain,a)}function v(e){return function(a){for(var r=arguments.length,t=new Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];return e(h(a,a.domain?void 0:d),...t)}}const P=v(a.redirect),g=v(a.permanentRedirect);return{config:s,Link:p,redirect:P,permanentRedirect:g,getPathname:h}};
