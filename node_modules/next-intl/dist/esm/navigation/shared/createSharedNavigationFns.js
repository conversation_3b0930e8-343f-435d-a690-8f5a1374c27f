import{extends as e}from"../../_virtual/_rollupPluginBabelHelpers.js";import{redirect as o,permanentRedirect as a}from"next/navigation";import t,{forwardRef as n,use as r}from"react";import{receiveRoutingConfig as l}from"../../routing/config.js";import{isLocalizableHref as i}from"../../shared/utils.js";import m from"./BaseLink.js";import{validateReceivedConfig as c,serializeSearchParams as f,compileLocalizedPathname as s,applyPathnamePrefix as u,normalizeNameOrNameWithParams as p}from"./utils.js";function d(d,h){const j=l(h||{});c(j);const g=j.pathnames,v="as-needed"===j.localePrefix.mode&&j.domains||void 0;function y(o,a){let n,l,{href:c,locale:f,...s}=o;"object"==typeof c?(n=c.pathname,l=c.params):n=c;const u=i(c),p=d(),h=p instanceof Promise?r(p):p,y=u?L({locale:f||h,href:null==g?n:{pathname:n,params:l}},null!=f||v||void 0):n;return t.createElement(m,e({ref:a,defaultLocale:j.defaultLocale,href:"object"==typeof c?{...c,pathname:y}:y,locale:f,localeCookie:j.localeCookie,unprefixed:v&&u?{domains:j.domains.reduce(((e,o)=>(e[o.domain]=o.defaultLocale,e)),{}),pathname:L({locale:h,href:null==g?n:{pathname:n,params:l}},!1)}:void 0},s))}const x=n(y);function L(e,o){const{href:a,locale:t}=e;let n;return null==g?"object"==typeof a?(n=a.pathname,a.query&&(n+=f(a.query))):n=a:n=s({locale:t,...p(a),pathnames:j.pathnames}),u(n,t,j,e.domain,o)}function b(e){return function(o){for(var a=arguments.length,t=new Array(a>1?a-1:0),n=1;n<a;n++)t[n-1]=arguments[n];return e(L(o,o.domain?void 0:v),...t)}}const k=b(o),P=b(a);return{config:j,Link:x,redirect:k,permanentRedirect:P,getPathname:L}}export{d as default};
