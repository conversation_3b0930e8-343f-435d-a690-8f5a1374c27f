import{notFound as e}from"next/navigation";import{cache as t}from"react";import{_createIntlFormatters as n,_createCache as o,initializeConfig as r}from"use-intl/core";import{getRequestLocale as i}from"./RequestLocale.js";import{getRequestLocale as a}from"./RequestLocaleLegacy.js";import l from"next-intl/config";let s=!1,c=!1;const u=t((function(){return new Date}));const f=t((function(){return Intl.DateTimeFormat().resolvedOptions().timeZone}));const d=t((async function(t,n){if("function"!=typeof t)throw new Error("Invalid i18n request configuration detected.\n\nPlease verify that:\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\n2. You have a default export in your i18n request configuration file.\n\nSee also: https://next-intl.dev/docs/usage/configuration#i18n-request\n");const o={get locale(){return c||(console.warn("\nThe `locale` parameter in `getRequestConfig` is deprecated, please switch to `await requestLocale`. See https://next-intl.dev/blog/next-intl-3-22#await-request-locale\n"),c=!0),n||a()},get requestLocale(){return n?Promise.resolve(n):i()}};let r=t(o);r instanceof Promise&&(r=await r);let l=r.locale;return l||(s||(console.error("\nA `locale` is expected to be returned from `getRequestConfig`, but none was returned. This will be an error in the next major version of next-intl.\n\nSee: https://next-intl.dev/blog/next-intl-3-22#await-request-locale\n"),s=!0),l=await o.requestLocale,l||(console.error("\nUnable to find `next-intl` locale because the middleware didn't run on this request and no `locale` was returned in `getRequestConfig`. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\n"),e())),{...r,locale:l,now:r.now||u(),timeZone:r.timeZone||f()}})),m=t(n),g=t(o);const p=t((async function(e){const t=await d(l,e);return{...r(t),_formatters:m(g())}}));export{p as default};
