function n(n){return function(n){return"object"==typeof n?null==n.host&&null==n.hostname:!/^[a-z]+:/i.test(n)}(n)&&!function(n){const t="object"==typeof n?n.pathname:n;return null!=t&&!t.startsWith("/")}(n)}function t(t,r){let u=arguments.length>2&&void 0!==arguments[2]?arguments[2]:r,c=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0;if(!n(t))return t;const f=r!==u,l=i(o,c);return(f||l)&&null!=o?e(t,o):t}function e(n,t){let e;return"string"==typeof n?e=u(t,n):(e={...n},n.pathname&&(e.pathname=u(t,n.pathname))),e}function r(n,t){return n.replace(new RegExp("^".concat(t)),"")||"/"}function u(n,t){let e=n;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),e+=t,e}function i(n,t){return t===n||t.startsWith("".concat(n,"/"))}function c(n){const t=function(){try{return"true"===process.env._next_intl_trailing_slash}catch(n){return!1}}();if("/"!==n){const e=n.endsWith("/");t&&!e?n+="/":!t&&e&&(n=n.slice(0,-1))}return n}function o(n,t){const e=c(n),r=c(t);return s(e).test(r)}function f(n,t){var e;return"never"!==t.mode&&(null===(e=t.prefixes)||void 0===e?void 0:e[n])||l(n)}function l(n){return"/"+n}function s(n){const t=n.replace(/\[\[(\.\.\.[^\]]+)\]\]/g,"?(.*)").replace(/\[(\.\.\.[^\]]+)\]/g,"(.+)").replace(/\[([^\]]+)\]/g,"([^/]+)");return new RegExp("^".concat(t,"$"))}function a(n){return n.includes("[[...")}function p(n){return n.includes("[...")}function h(n){return n.includes("[")}function g(n,t){const e=n.split("/"),r=t.split("/"),u=Math.max(e.length,r.length);for(let n=0;n<u;n++){const t=e[n],u=r[n];if(!t&&u)return-1;if(t&&!u)return 1;if(t||u){if(!h(t)&&h(u))return-1;if(h(t)&&!h(u))return 1;if(!p(t)&&p(u))return-1;if(p(t)&&!p(u))return 1;if(!a(t)&&a(u))return-1;if(a(t)&&!a(u))return 1}}return 0}function d(n){return n.sort(g)}export{l as getLocaleAsPrefix,f as getLocalePrefix,d as getSortedPathnames,i as hasPathnamePrefixed,n as isLocalizableHref,t as localizeHref,o as matchesPathname,c as normalizeTrailingSlash,e as prefixHref,u as prefixPathname,s as templateToRegex,r as unprefixPathname};
