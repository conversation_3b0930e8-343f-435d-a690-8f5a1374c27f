import { UrlObject } from 'url';
import NextLink from 'next/link';
import { ComponentProps } from 'react';
import { LocalePrefixConfigVerbose, LocalePrefixMode, Locales } from '../routing/types';
type Href = ComponentProps<typeof NextLink>['href'];
export declare function isLocalizableHref(href: Href): boolean;
export declare function localizeHref(href: string, locale: string, curLocale: string, curPathname: string, prefix?: string): string;
export declare function localizeHref(href: UrlObject | string, locale: string, curLocale: string, curPathname: string, prefix?: string): UrlObject | string;
export declare function prefixHref(href: string, prefix: string): string;
export declare function prefixHref(href: UrlObject | string, prefix: string): UrlObject | string;
export declare function unprefixPathname(pathname: string, prefix: string): string;
export declare function prefixPathname(prefix: string, pathname: string): string;
export declare function hasPathnamePrefixed(prefix: string | undefined, pathname: string): boolean;
export declare function normalizeTrailingSlash(pathname: string): string;
export declare function matchesPathname(
/** E.g. `/users/[userId]-[userName]` */
template: string, 
/** E.g. `/users/23-jane` */
pathname: string): boolean;
export declare function getLocalePrefix<AppLocales extends Locales, AppLocalePrefixMode extends LocalePrefixMode>(locale: AppLocales[number], localePrefix: LocalePrefixConfigVerbose<AppLocales, AppLocalePrefixMode>): string;
export declare function getLocaleAsPrefix(locale: string): string;
export declare function templateToRegex(template: string): RegExp;
export declare function getSortedPathnames(pathnames: Array<string>): string[];
export {};
