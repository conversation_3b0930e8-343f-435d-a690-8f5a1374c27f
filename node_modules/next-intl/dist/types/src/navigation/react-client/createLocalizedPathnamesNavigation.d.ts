import React, { ComponentProps, ReactElement } from 'react';
import { RoutingConfigLocalizedNavigation } from '../../routing/config';
import { DomainsConfig, LocalePrefixMode, Locales, Pathnames } from '../../routing/types';
import { HrefOrHrefWithParams } from '../shared/utils';
import ClientLink from './ClientLink';
/**
 * @deprecated Consider switching to `createNavigation` (see https://next-intl.dev/blog/next-intl-3-22#create-navigation)
 **/
export default function createLocalizedPathnamesNavigation<AppLocales extends Locales, AppLocalePrefixMode extends LocalePrefixMode = 'always', AppPathnames extends Pathnames<AppLocales> = never, AppDomains extends DomainsConfig<AppLocales> = never>(routing: RoutingConfigLocalizedNavigation<AppLocales, AppLocalePrefixMode, AppPathnames, AppDomains>): {
    Link: <Pathname extends keyof AppPathnames>(props: (Omit<Omit<Omit<Omit<Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof {
        href: string | import("url").UrlObject;
        as?: string | import("url").UrlObject;
        replace?: boolean;
        scroll?: boolean;
        shallow?: boolean;
        passHref?: boolean;
        prefetch?: boolean | null;
        locale?: string | false;
        legacyBehavior?: boolean;
        onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>;
        onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>;
        onClick?: React.MouseEventHandler<HTMLAnchorElement>;
    }> & {
        href: string | import("url").UrlObject;
        as?: string | import("url").UrlObject;
        replace?: boolean;
        scroll?: boolean;
        shallow?: boolean;
        passHref?: boolean;
        prefetch?: boolean | null;
        locale?: string | false;
        legacyBehavior?: boolean;
        onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>;
        onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>;
        onClick?: React.MouseEventHandler<HTMLAnchorElement>;
    } & {
        children?: React.ReactNode;
    } & React.RefAttributes<HTMLAnchorElement>, "locale"> & {
        locale: string;
        prefix: string;
        localePrefixMode: LocalePrefixMode;
        localeCookie: import("../../routing/config").InitializedLocaleCookieConfig;
    }, "ref"> & React.RefAttributes<HTMLAnchorElement>, "locale" | "prefix" | "localePrefixMode"> & {
        locale?: string | undefined;
        localePrefix: {
            mode: "never";
        } | {
            mode: "always";
            prefixes?: Partial<Record<string, string>> | undefined;
        } | {
            mode: "as-needed";
            prefixes?: Partial<Record<string, string>> | undefined;
        };
    } & {
        ref?: React.LegacyRef<HTMLAnchorElement> | undefined;
    }, "name" | "localePrefix" | "localeCookie" | "href"> & {
        href: Pathname extends `${string}[[...${string}` ? Pathname | ({
            pathname: Pathname;
            params?: import("../shared/StrictParams").default<Pathname> | undefined;
        } & Omit<import("url").UrlObject, "pathname">) : Pathname extends `${string}[${string}` ? {
            pathname: Pathname;
            params: import("../shared/StrictParams").default<Pathname>;
        } & Omit<import("url").UrlObject, "pathname"> : Pathname | ({
            pathname: Pathname;
        } & Omit<import("url").UrlObject, "pathname">);
        locale?: AppLocales[number];
    }) & {
        ref?: ComponentProps<typeof ClientLink>["ref"];
    }) => ReactElement;
    redirect: <Pathname extends keyof AppPathnames>(href: HrefOrHrefWithParams<Pathname>, type?: import("next/navigation").RedirectType | undefined) => never;
    permanentRedirect: <Pathname extends keyof AppPathnames>(href: HrefOrHrefWithParams<Pathname>, type?: import("next/navigation").RedirectType | undefined) => never;
    usePathname: () => keyof AppPathnames;
    useRouter: () => {
        push<Pathname extends keyof AppPathnames>(href: HrefOrHrefWithParams<Pathname>, options?: (import("next/dist/shared/lib/app-router-context.shared-runtime").NavigateOptions & {
            locale?: string | undefined;
        }) | undefined): void;
        replace<Pathname extends keyof AppPathnames>(href: HrefOrHrefWithParams<Pathname>, options?: (import("next/dist/shared/lib/app-router-context.shared-runtime").NavigateOptions & {
            locale?: string | undefined;
        }) | undefined): void;
        prefetch<Pathname extends keyof AppPathnames>(href: HrefOrHrefWithParams<Pathname>, options?: (import("next/dist/shared/lib/app-router-context.shared-runtime").PrefetchOptions & {
            locale?: string | undefined;
        }) | undefined): void;
        back(): void;
        forward(): void;
        refresh(): void;
    };
    getPathname: ({ href, locale }: {
        locale: AppLocales[number];
        href: HrefOrHrefWithParams<keyof AppPathnames>;
    }) => string;
};
