{"name": "intl-messageformat", "version": "10.7.14", "description": "Formats ICU Message strings with number, date, plural, and select placeholders to create localized messages.", "keywords": ["i18n", "intl", "internationalization", "localization", "globalization", "messageformat", "parser", "plural", "icu"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "Caridy <PERSON>ino <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "bugs": {"url": "https://github.com/formatjs/formatjs/issues"}, "repository": {"type": "git", "url": "**************:formatjs/formatjs.git"}, "main": "index.js", "module": "lib/index.js", "types": "index.d.ts", "dependencies": {"tslib": "2", "@formatjs/ecma402-abstract": "2.3.2", "@formatjs/icu-messageformat-parser": "2.11.0", "@formatjs/fast-memoize": "2.2.6"}, "sideEffects": false, "homepage": "https://github.com/formatjs/formatjs", "license": "BSD-3-<PERSON><PERSON>", "gitHead": "a7842673d8ad205171ad7c8cb8bb2f318b427c0c"}