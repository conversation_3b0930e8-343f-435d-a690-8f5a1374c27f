"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/not-found",{

/***/ "(app-pages-browser)/./components/layout/Layout.js":
/*!*************************************!*\
  !*** ./components/layout/Layout.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Layout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _elements_BackToTop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../elements/BackToTop */ \"(app-pages-browser)/./components/elements/BackToTop.js\");\n/* harmony import */ var _elements_DataBg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../elements/DataBg */ \"(app-pages-browser)/./components/elements/DataBg.js\");\n/* harmony import */ var _Breadcrumb__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Breadcrumb */ \"(app-pages-browser)/./components/layout/Breadcrumb.js\");\n/* harmony import */ var _SearchPopup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./SearchPopup */ \"(app-pages-browser)/./components/layout/SearchPopup.js\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Sidebar */ \"(app-pages-browser)/./components/layout/Sidebar.js\");\n/* harmony import */ var _header_Header1__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./header/Header1 */ \"(app-pages-browser)/./components/layout/header/Header1.js\");\n/* harmony import */ var _header_Header2__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./header/Header2 */ \"(app-pages-browser)/./components/layout/header/Header2.js\");\n/* harmony import */ var _header_Header3__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./header/Header3 */ \"(app-pages-browser)/./components/layout/header/Header3.js\");\n/* harmony import */ var _header_Header4__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./header/Header4 */ \"(app-pages-browser)/./components/layout/header/Header4.js\");\n/* harmony import */ var _footer_Footer1__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./footer/Footer1 */ \"(app-pages-browser)/./components/layout/footer/Footer1.js\");\n/* harmony import */ var _footer_Footer2__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./footer/Footer2 */ \"(app-pages-browser)/./components/layout/footer/Footer2.js\");\n/* harmony import */ var _footer_Footer3__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./footer/Footer3 */ \"(app-pages-browser)/./components/layout/footer/Footer3.js\");\n/* harmony import */ var _footer_Footer4__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./footer/Footer4 */ \"(app-pages-browser)/./components/layout/footer/Footer4.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Layout(param) {\n    let { headerStyle, footerStyle, headTitle, breadcrumbTitle, children, wrapperCls } = param;\n    _s();\n    const [scroll, setScroll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Mobile Menu\n    const [isMobileMenu, setMobileMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleMobileMenu = ()=>{\n        setMobileMenu(!isMobileMenu);\n        !isMobileMenu ? document.body.classList.add(\"mobile-menu-visible\") : document.body.classList.remove(\"mobile-menu-visible\");\n    };\n    // Popup\n    const [isPopup, setPopup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handlePopup = ()=>setPopup(!isPopup);\n    // Sidebar\n    const [isSidebar, setSidebar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSidebar = ()=>setSidebar(!isSidebar);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize WOW.js safely\n        try {\n            const WOW = __webpack_require__(/*! wowjs */ \"(app-pages-browser)/./node_modules/wowjs/dist/wow.js\");\n            if (true) {\n                window.wow = new WOW.WOW({\n                    live: false\n                });\n                window.wow.init();\n            }\n        } catch (error) {\n            console.warn(\"WOW.js initialization failed:\", error);\n        }\n        // Add scroll listener\n        const handleScroll = ()=>{\n            const scrollCheck = window.scrollY > 100;\n            if (scrollCheck !== scroll) {\n                setScroll(scrollCheck);\n            }\n        };\n        document.addEventListener(\"scroll\", handleScroll);\n        // Cleanup function\n        return ()=>{\n            document.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, [\n        scroll\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_elements_DataBg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/layout/Layout.js\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"page-wrapper \".concat(wrapperCls ? wrapperCls : \"\"),\n                id: \"#top\",\n                children: [\n                    !headerStyle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header_Header1__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        scroll: scroll,\n                        isMobileMenu: isMobileMenu,\n                        handleMobileMenu: handleMobileMenu,\n                        handlePopup: handlePopup,\n                        isSidebar: isSidebar,\n                        handleSidebar: handleSidebar\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/layout/Layout.js\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this),\n                    headerStyle == 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header_Header1__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        scroll: scroll,\n                        isMobileMenu: isMobileMenu,\n                        handleMobileMenu: handleMobileMenu,\n                        handlePopup: handlePopup,\n                        isSidebar: isSidebar,\n                        handleSidebar: handleSidebar\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/layout/Layout.js\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this) : null,\n                    headerStyle == 2 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header_Header2__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        scroll: scroll,\n                        isMobileMenu: isMobileMenu,\n                        handleMobileMenu: handleMobileMenu,\n                        handlePopup: handlePopup,\n                        isSidebar: isSidebar,\n                        handleSidebar: handleSidebar\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/layout/Layout.js\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this) : null,\n                    headerStyle == 3 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header_Header3__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        scroll: scroll,\n                        isMobileMenu: isMobileMenu,\n                        handleMobileMenu: handleMobileMenu,\n                        handlePopup: handlePopup,\n                        isSidebar: isSidebar,\n                        handleSidebar: handleSidebar\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/layout/Layout.js\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this) : null,\n                    headerStyle == 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header_Header4__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        scroll: scroll,\n                        isMobileMenu: isMobileMenu,\n                        handleMobileMenu: handleMobileMenu,\n                        handlePopup: handlePopup,\n                        isSidebar: isSidebar,\n                        handleSidebar: handleSidebar\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/layout/Layout.js\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this) : null,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        isSidebar: isSidebar,\n                        handleSidebar: handleSidebar\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/layout/Layout.js\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SearchPopup__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        isPopup: isPopup,\n                        handlePopup: handlePopup\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/layout/Layout.js\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    breadcrumbTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Breadcrumb__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        breadcrumbTitle: breadcrumbTitle\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/layout/Layout.js\",\n                        lineNumber: 131,\n                        columnNumber: 29\n                    }, this),\n                    children,\n                    !footerStyle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_footer_Footer1__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/layout/Layout.js\",\n                        lineNumber: 135,\n                        columnNumber: 26\n                    }, this),\n                    footerStyle == 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_footer_Footer1__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/layout/Layout.js\",\n                        lineNumber: 136,\n                        columnNumber: 29\n                    }, this) : null,\n                    footerStyle == 2 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_footer_Footer2__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/layout/Layout.js\",\n                        lineNumber: 137,\n                        columnNumber: 29\n                    }, this) : null,\n                    footerStyle == 3 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_footer_Footer3__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/layout/Layout.js\",\n                        lineNumber: 138,\n                        columnNumber: 29\n                    }, this) : null,\n                    footerStyle == 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_footer_Footer4__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/layout/Layout.js\",\n                        lineNumber: 139,\n                        columnNumber: 29\n                    }, this) : null\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/layout/Layout.js\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_elements_BackToTop__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                scroll: scroll\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/layout/Layout.js\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Layout, \"c6GMDAFyDoSaqgOVwnfQK2dMBbg=\");\n_c = Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/layout/Layout.js\n"));

/***/ })

});