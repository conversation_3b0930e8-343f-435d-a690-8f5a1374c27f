/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./public/assets/css/module-css/cookiesconsent.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
.cookies-consent {
   position: fixed;
   bottom: 0;
   left: 0;
   right: 0;
   background-color: rgba(25, 26, 33, 0.9);
   color: white;
   padding: 20px;
   display: flex;
   flex-direction: column;
   align-items: center;
   justify-content: center;
   z-index: 999;
}

.cookies-consent2 {
   width: 70%;
   background-color: rgba(247, 247, 247, 0.9);
   border: 3px solid #325ae1;
   box-shadow: rgba(0, 0, 0, 0.6) 0px 1px 16px;
   box-sizing: border-box;
   margin: 16px auto;
   position: fixed;
   padding: 0px 24px;
   left: 16px;
   /* right: 16px; */
   bottom: 0px;
   z-index: 999;
}

.cookies-consent__message {
   /* margin-bottom: 10px; */
   /* text-align: center; */
   box-sizing: border-box;
   padding: 16px 0px;
   display: flex;
   justify-content: space-evenly;
   font-size: 1rem;
   line-height: 1.5;
   align-items: center;
}

.cookies-consent__message p {
   margin-right: 1%;
   font-size: 13px;
}

.cookies-consent__message a {
   color: #325ae1;
   font-weight: 600;
}

.cookies-consent__actions {
   display: flex;
   gap: 10px;
}

.cookies-consent__button {
   position: relative;
   display: inline-block;
   color: white;
   font-size: 14px;
   line-height: 50px;
   font-weight: 700;
   background: var(--logistiq-base);
   border-radius: 7px;
   padding: 1px 25px 0px;
   /* overflow: hidden; */
   font-family: var(--logistiq-font);
   text-transform: capitalize;
   transition: all 0.3slinear;
   z-index: 1;
}

.cookies-consent__button--accept {
   background-color: var(--logistiq-base);
   color: white;
}

.cookies-consent__button--decline {
   background-color: #f44336;
   color: white;
}

@media screen and (max-width: 600px) {
   .cookies-consent {
      flex-direction: row;
      padding: 10px;
   }

   .cookies-consent__message {
      flex-direction: column;
      align-items: center;
      justify-content: center;
   }
   .cookies-consent__message p {
      margin-right: 0;
      margin-bottom: 10px;
   }

   .cookies-consent__actions {
      flex-direction: column;
      width: 100%;
   }

   /* .cookies-consent__button {
      width: 40%;
      margin-bottom: 10px;
   } */

   .cookies-consent__button {
      font-size: 12px;
      line-height: 30px;
      padding: 1px 15px 0px;
   }

   .cookies-consent__message p {
      font-size: 10px;
   }
}

/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./public/assets/css/module-css/chat-button.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
.chatContainer {
   position: fixed;
   bottom: 20px;
   right: 20px;
   justify-items: center;
   z-index: 1000;
}

.chatTitle {
   font-size: 11px;
   font-weight: 600;
   color: var(--logistiq-white);
   padding: 5px 5px;
   background-color: var(--logistiq-base);
   border: none;
   border-radius: 5px;
   box-shadow: 0 0 5px rgba(6, 46, 57, 0.7);
   margin-bottom: 5px;
}

.iconConatiner {
   display: flex;
   flex-direction: column;
   align-items: center;
   position: absolute;
   bottom: 60px;
   right: 0;
}

.iconConatiner .fab {
   display: flex;
}

.iconConatiner a:hover {
   color: inherit;
}

.msgIcon {
   font-size: 2rem;
   margin-bottom: 10px;
   color: white;
   position: absolute;
   bottom: -5px;
   right: 28px;
   width: 55px;
   height: 55px;
   background-color: #1877f2;
   border-radius: 100%;
   box-shadow: 0 0 7px rgba(0, 0, 0, 0.5);
   display: flex;
   align-items: center;
   justify-content: center;
}

.lineIcon {
   font-size: 55px;
   margin-bottom: 0px;
   color: #06c755;
   position: absolute;
   top: 4px;
   right: 70px;
   width: 48px;
   height: 48px;

   background-color: white;
   border-radius: 10px;
   box-shadow: 0 0 7px rgba(0, 0, 0, 0.5);
   display: flex;
   align-items: center;
   justify-content: center;
}

.chatButton {
   cursor: pointer;
   /* padding: 10px 20px; */
   font-size: 1.6rem;
   width: 60px;
   height: 60px;
   background-color: var(--logistiq-base);
   color: var(--logistiq-white);
   border: none;
   border-radius: 100px;
   box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

/* .chatButton:hover {
   border-radius: 100px;
   background: var(--logistiq-gray);
} */
.chatButton .close {
   width: 100%;
   height: 100%;
   background-color: var(--logistiq-white);
   opacity: 0.9;
   border-radius: 100px;
   display: flex;
   align-items: center;
   justify-content: center;
   color: var(--logistiq-gray);
}

.chatWindow {
   position: fixed;
   bottom: 60px;
   right: 20px;
   width: 300px;
   height: 400px;
   background-color: white;
   border: 1px solid #ccc;
   border-radius: 10px;
   box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

