{"node": {"00b6db0d437a376d2c6d731b450aae3025370229": {"workers": {"app/[locale]/page": "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fnopparattk%2FDocuments%2FSAK%20WoodWorks%2FWebsite%2FLanding%20page%2Fdeveloping%2Fintl2-companyprofile-v02%2Fi18n%2Flocale.js%22%2C%5B%22setUserLocale%22%2C%22getUserLocale%22%5D%5D%5D&__client_imported__=!"}, "layer": {"app/[locale]/page": "rsc"}}, "4da1f915bac7bf024cf8588b7eb56f180af9195b": {"workers": {"app/[locale]/page": "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fnopparattk%2FDocuments%2FSAK%20WoodWorks%2FWebsite%2FLanding%20page%2Fdeveloping%2Fintl2-companyprofile-v02%2Fi18n%2Flocale.js%22%2C%5B%22setUserLocale%22%2C%22getUserLocale%22%5D%5D%5D&__client_imported__=!"}, "layer": {"app/[locale]/page": "rsc"}}}, "edge": {}, "encryptionKey": "MEBggiEgvCJAMtVxjnx5BaL39cTbmBV3HsNgM0hUHMA="}