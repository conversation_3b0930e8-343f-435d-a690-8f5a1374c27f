self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"00b6db0d437a376d2c6d731b450aae3025370229\": {\n      \"workers\": {\n        \"app/[locale]/page\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fnopparattk%2FDocuments%2FSAK%20WoodWorks%2FWebsite%2FLanding%20page%2Fdeveloping%2Fintl2-companyprofile-v02%2Fi18n%2Flocale.js%22%2C%5B%22setUserLocale%22%2C%22getUserLocale%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/[locale]/page\": \"rsc\"\n      }\n    },\n    \"4da1f915bac7bf024cf8588b7eb56f180af9195b\": {\n      \"workers\": {\n        \"app/[locale]/page\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fnopparattk%2FDocuments%2FSAK%20WoodWorks%2FWebsite%2FLanding%20page%2Fdeveloping%2Fintl2-companyprofile-v02%2Fi18n%2Flocale.js%22%2C%5B%22setUserLocale%22%2C%22getUserLocale%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/[locale]/page\": \"rsc\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"